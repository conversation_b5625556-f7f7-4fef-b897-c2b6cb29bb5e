#!/bin/bash

# Script to generate .env file from CDK outputs
# Usage: ./scripts/generate-env.sh [environment]

ENVIRONMENT=${1:-dev}
STACK_NAME="TradeManageStack-${ENVIRONMENT}"

echo "Generating .env file from CDK outputs for environment: ${ENVIRONMENT}"

# Get CDK outputs
CDK_OUTPUTS=$(aws cloudformation describe-stacks --stack-name ${STACK_NAME} --query 'Stacks[0].Outputs' --output json)

if [ $? -ne 0 ]; then
    echo "Error: Failed to get CDK outputs. Make sure the stack is deployed and you have AWS CLI configured."
    exit 1
fi

# Extract the DotEnvFileContent output
ENV_CONTENT=$(echo ${CDK_OUTPUTS} | jq -r '.[] | select(.OutputKey=="DotEnvFileContent") | .OutputValue')

if [ "${ENV_CONTENT}" == "null" ] || [ -z "${ENV_CONTENT}" ]; then
    echo "Error: DotEnvFileContent output not found in CDK stack outputs."
    echo "Available outputs:"
    echo ${CDK_OUTPUTS} | jq -r '.[] | .OutputKey'
    exit 1
fi

# Write to .env file
echo "${ENV_CONTENT}" > .env

echo "✅ .env file generated successfully!"
echo "📁 Content written to: .env"
echo ""
echo "🔧 Next steps:"
echo "1. Review the .env file and add any missing local development variables"
echo "2. If running locally, you may need to set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY"
echo "3. Restart your application: npm run start:dev"
