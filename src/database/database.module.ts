import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';

import { DynamodbService } from './dynamodb.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'DYNAMODB_CLIENT',
      useFactory: (configService: ConfigService) => {
        const region = configService.get<string>('aws.region');
        const accessKeyId = configService.get<string>('aws.accessKeyId');
        const secretAccessKey = configService.get<string>('aws.secretAccessKey');

        // Build client configuration
        const clientConfig: any = { region };

        // Only set explicit credentials if both are provided
        // Otherwise, let AWS SDK use default credential chain (AWS CLI, IAM roles, etc.)
        if (accessKeyId && secretAccessKey) {
          clientConfig.credentials = {
            accessKeyId,
            secretAccessKey,
          };
        }

        const client = new DynamoDBClient(clientConfig);
        return DynamoDBDocumentClient.from(client);
      },
      inject: [ConfigService],
    },
    DynamodbService,
  ],
  exports: ['DYNAMODB_CLIENT', DynamodbService],
})
export class DatabaseModule {}
