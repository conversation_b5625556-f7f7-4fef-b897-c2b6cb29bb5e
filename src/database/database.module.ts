import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';

import { DynamodbService } from './dynamodb.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: 'DYNAMODB_CLIENT',
      useFactory: (configService: ConfigService) => {
        const region = configService.get<string>('aws.region');
        const accessKeyId = configService.get<string>('aws.accessKeyId');
        const secretAccessKey = configService.get<string>('aws.secretAccessKey');

        // Require explicit AWS credentials for all environments
        if (!accessKeyId || !secretAccessKey) {
          throw new Error(
            'AWS credentials are required. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY in your environment variables.'
          );
        }

        const client = new DynamoDBClient({
          region,
          credentials: {
            accessKeyId,
            secretAccessKey,
          },
        });
        return DynamoDBDocumentClient.from(client);
      },
      inject: [ConfigService],
    },
    DynamodbService,
  ],
  exports: ['DYNAMODB_CLIENT', DynamodbService],
})
export class DatabaseModule {}
